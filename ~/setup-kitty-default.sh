#!/bin/bash

echo "🌆 CONFIGURANDO KITTY COMO TERMINAL PADRÃO 🌆"

# Definir Kitty como terminal padrão do sistema
echo "🔧 Configurando Kitty como terminal padrão..."
sudo update-alternatives --install /usr/bin/x-terminal-emulator x-terminal-emulator /usr/bin/kitty 50
sudo update-alternatives --set x-terminal-emulator /usr/bin/kitty

# Configurar no GNOME
echo "🖥️ Configurando no GNOME..."
gsettings set org.gnome.desktop.default-applications.terminal exec 'kitty'
gsettings set org.gnome.desktop.default-applications.terminal exec-arg ''

# Configurar variáveis de ambiente
echo "🌐 Configurando variáveis de ambiente..."
echo 'export TERMINAL=kitty' >> ~/.bashrc
echo 'export TERM=xterm-kitty' >> ~/.bashrc

# Criar link simbólico para compatibilidade
echo "🔗 Criando links de compatibilidade..."
sudo ln -sf /usr/bin/kitty /usr/local/bin/gnome-terminal 2>/dev/null || true
sudo ln -sf /usr/bin/kitty /usr/local/bin/xterm 2>/dev/null || true

# Configurar para i3
echo "⚙️ Configurando para i3..."
if [ -f ~/.config/i3/config ]; then
    sed -i 's/set \$term .*/set \$term kitty/' ~/.config/i3/config
fi

# Configurar atalhos do sistema
echo "⌨️ Configurando atalhos do sistema..."
gsettings set org.gnome.settings-daemon.plugins.media-keys terminal "['<Primary><Alt>t']"

echo ""
echo "✅ KITTY CONFIGURADO COMO TERMINAL PADRÃO!"
echo ""
echo "🎯 ATALHOS DE TRANSPARÊNCIA NO KITTY:"
echo "• Ctrl+Shift+A, M: Aumentar transparência"
echo "• Ctrl+Shift+A, L: Diminuir transparência"
echo "• Ctrl+Shift+A, 1: Opacidade total"
echo "• Ctrl+Shift+A, D: Transparência padrão"
echo ""
echo "🔥 OUTROS ATALHOS ÚTEIS:"
echo "• Ctrl+Shift+Enter: Nova janela"
echo "• Ctrl+Shift+T: Nova aba"
echo "• Ctrl+Shift+W: Fechar janela"
echo "• Ctrl+Shift+Q: Fechar aba"
echo "• Ctrl+Shift+F5: Recarregar configuração"
echo "• Ctrl+Shift+Plus/Minus: Aumentar/Diminuir fonte"
echo ""
echo "🌆 CARACTERÍSTICAS CYBERPUNK:"
echo "• Transparência: 80% (ajustável)"
echo "• Blur de fundo: 20px"
echo "• Cores neon personalizadas"
echo "• Bordas com glow effect"
echo "• Tamanho inicial: 1400x900"
echo ""
echo "🚀 Reinicie o terminal para aplicar todas as mudanças!"
