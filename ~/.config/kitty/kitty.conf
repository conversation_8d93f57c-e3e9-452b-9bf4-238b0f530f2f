# 🌆 CYBERPUNK NEON HACKER THEME 🌆
# Terminal transparente com cores neon

# Font configuration
font_family      JetBrains Mono Nerd Font
bold_font        auto
italic_font      auto
bold_italic_font auto
font_size 13.0

# 🌆 CYBERPUNK TRANSPARENCY SETTINGS 🌆
background_opacity 0.80
dynamic_background_opacity yes

# Advanced transparency settings
background_blur 20
background_tint 0.8
background_tint_gaps -0.3

# Window settings for better transparency
remember_window_size yes
initial_window_width 1400
initial_window_height 900

# Confirm close when multiple tabs
confirm_os_window_close 1

# Cursor
cursor_shape block
cursor_blink_interval 0.8
cursor_stop_blinking_after 15.0

# Scrollback
scrollback_lines 10000
scrollback_pager less --chop-long-lines --RAW-CONTROL-CHARS +INPUT_LINE_NUMBER
scrollback_pager_history_size 0
scrollback_fill_enlarged_window no
wheel_scroll_multiplier 5.0
touch_scroll_multiplier 1.0

# Mouse
mouse_hide_wait 3.0
url_color #ff00ff
url_style curly
open_url_with default
url_prefixes http https file ftp gemini irc gopher mailto news git
detect_urls yes
copy_on_select no
strip_trailing_spaces never
select_by_word_characters @-./_~?&=%+#
click_interval -1.0
focus_follows_mouse no
pointer_shape_when_grabbed arrow
default_pointer_shape beam
pointer_shape_when_dragging beam

# Performance tuning
repaint_delay 10
input_delay 3
sync_to_monitor yes

# Terminal bell
enable_audio_bell no
visual_bell_duration 0.0
window_alert_on_bell yes
bell_on_tab yes
command_on_bell none

# Window layout (updated above in transparency section)
enabled_layouts *
window_resize_step_cells 2
window_resize_step_lines 2
window_border_width 2pt
draw_minimal_borders yes
window_margin_width 5
single_window_margin_width -1
window_padding_width 10
placement_strategy center
active_border_color #ff00ff
inactive_border_color #00ffff
bell_border_color #ff0080
inactive_text_alpha 0.8
hide_window_decorations no
resize_debounce_time 0.1
resize_draw_strategy static
resize_in_steps no
confirm_os_window_close 0

# Tab bar
tab_bar_edge bottom
tab_bar_margin_width 0.0
tab_bar_margin_height 0.0 0.0
tab_bar_style powerline
tab_bar_min_tabs 2
tab_switch_strategy previous
tab_fade 0.25 0.5 0.75 1
tab_separator " ▌"
tab_powerline_style angled
tab_activity_symbol "⚡"
tab_title_template "🔥 {title}"
active_tab_title_template none
active_tab_foreground   #000000
active_tab_background   #ff00ff
active_tab_font_style   bold-italic
inactive_tab_foreground #00ffff
inactive_tab_background #1a0033
inactive_tab_font_style normal

# 🌈 CYBERPUNK NEON COLOR SCHEME 🌈
foreground #00ffff
background #0a0a0f
selection_foreground #000000
selection_background #ff00ff

# Cursor colors
cursor #ff00ff
cursor_text_color #000000

# URL underline color when hovering with mouse
url_color #ff00ff

# Kitty window border colors
active_border_color #ff00ff
inactive_border_color #00ffff
bell_border_color #ff0080

# OS Window titlebar colors
wayland_titlebar_color #0a0a0f
macos_titlebar_color #0a0a0f

# Tab bar colors
active_tab_foreground   #000000
active_tab_background   #ff00ff
inactive_tab_foreground #00ffff
inactive_tab_background #1a0033
tab_bar_background      #0a0a0f

# Colors for marks (marked text in the terminal)
mark1_foreground #000000
mark1_background #ff00ff
mark2_foreground #000000
mark2_background #00ffff
mark3_foreground #000000
mark3_background #ff0080

# 🎨 CYBERPUNK NEON PALETTE 🎨
# normal colors
color0 #0a0a0f    # black (background)
color1 #ff0080    # red (neon pink)
color2 #00ff41    # green (matrix green)
color3 #ffff00    # yellow (electric yellow)
color4 #0080ff    # blue (cyber blue)
color5 #ff00ff    # magenta (neon magenta)
color6 #00ffff    # cyan (electric cyan)
color7 #ffffff    # white

# bright colors
color8 #4d4d4d     # bright black
color9 #ff3399     # bright red
color10 #66ff66    # bright green
color11 #ffff66    # bright yellow
color12 #3399ff    # bright blue
color13 #ff66ff    # bright magenta
color14 #66ffff    # bright cyan
color15 #ffffff    # bright white

# Extended neon colors
color16 #ff6600    # neon orange
color17 #cc00ff    # electric purple

# 🌆 CYBERPUNK KEY MAPPINGS 🌆
# Basic clipboard
map ctrl+shift+c copy_to_clipboard
map ctrl+shift+v paste_from_clipboard

# Dynamic transparency controls
map ctrl+shift+a>m set_background_opacity +0.1
map ctrl+shift+a>l set_background_opacity -0.1
map ctrl+shift+a>1 set_background_opacity 1
map ctrl+shift+a>d set_background_opacity default

# Window management
map ctrl+shift+enter new_window
map ctrl+shift+w close_window
map ctrl+shift+] next_window
map ctrl+shift+[ previous_window

# Tab management
map ctrl+shift+t new_tab
map ctrl+shift+q close_tab
map ctrl+shift+right next_tab
map ctrl+shift+left previous_tab

# Font size
map ctrl+shift+equal change_font_size all +2.0
map ctrl+shift+minus change_font_size all -2.0
map ctrl+shift+backspace change_font_size all 0

# Reload config
map ctrl+shift+f5 load_config_file
map ctrl+shift+s paste_from_selection
map shift+insert paste_from_selection
map ctrl+shift+o pass_selection_to_program

# Window management
map ctrl+shift+enter new_window
map ctrl+shift+n new_os_window
map ctrl+shift+w close_window
map ctrl+shift+] next_window
map ctrl+shift+[ previous_window
map ctrl+shift+f move_window_forward
map ctrl+shift+b move_window_backward
map ctrl+shift+` move_window_to_top
map ctrl+shift+r start_resizing_window
map ctrl+shift+1 first_window
map ctrl+shift+2 second_window
map ctrl+shift+3 third_window
map ctrl+shift+4 fourth_window
map ctrl+shift+5 fifth_window
map ctrl+shift+6 sixth_window
map ctrl+shift+7 seventh_window
map ctrl+shift+8 eighth_window
map ctrl+shift+9 ninth_window
map ctrl+shift+0 tenth_window

# Tab management
map ctrl+shift+right next_tab
map ctrl+shift+left previous_tab
map ctrl+shift+t new_tab
map ctrl+shift+q close_tab
map ctrl+shift+. move_tab_forward
map ctrl+shift+, move_tab_backward
map ctrl+shift+alt+t set_tab_title

# Layout management
map ctrl+shift+l next_layout

# Font sizes
map ctrl+shift+equal change_font_size all +2.0
map ctrl+shift+plus change_font_size all +2.0
map ctrl+shift+kp_add change_font_size all +2.0
map ctrl+shift+minus change_font_size all -2.0
map ctrl+shift+kp_subtract change_font_size all -2.0
map ctrl+shift+backspace change_font_size all 0

# Select and act on visible text
map ctrl+shift+e kitten hints
map ctrl+shift+p>f kitten hints --type path --program -
map ctrl+shift+p>shift+f kitten hints --type path
map ctrl+shift+p>l kitten hints --type line --program -
map ctrl+shift+p>w kitten hints --type word --program -
map ctrl+shift+p>h kitten hints --type hash --program -
map ctrl+shift+p>n kitten hints --type linenum

# Miscellaneous
map ctrl+shift+f11 toggle_fullscreen
map ctrl+shift+f10 toggle_maximized
map ctrl+shift+u kitten unicode_input
map ctrl+shift+f2 edit_config_file
map ctrl+shift+escape kitty_shell window

# Sending arbitrary text on key presses
map ctrl+alt+enter send_text all \x1b[13;7u
map ctrl+alt+a send_text all Special text
map ctrl+shift+delete clear_terminal reset active

# You can open a new window running an arbitrary program
map ctrl+shift+y new_window less @selection

# You can open a new window with the current working directory set to the
# working directory of the current window.
map ctrl+alt+enter new_window_with_cwd

# You can open a new tab with the current working directory set to the
# working directory of the current window.
map ctrl+alt+t new_tab_with_cwd
